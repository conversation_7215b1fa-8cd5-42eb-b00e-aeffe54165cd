package com.rutong.medical.admin.constant;

import lombok.Getter;

/**
 * @ClassName AlarmWayConstant
 * @Description 设备类型
 * <AUTHOR>
 * @Date 2025/7/15 9:23
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Getter
public enum DeviceTypeEnum {

    /**
     * lora版报警器
     */
    INFRARED_MONITOR_433(6,SystemTypeEnum.WIRELESS_ALARM,"红外探测器"),
    /**
     * lora版报警器
     */
    SIREN_LORA(7,SystemTypeEnum.MEDICAL_SECURITY,"按钮"),
    /**
     * lora工卡
     */
    CARD_LORA(9,SystemTypeEnum.MEDICAL_SECURITY,"工卡"),
    /**
     * lora版红外探测
     */
    INFRARED_INTRUSION(8,SystemTypeEnum.WIRELESS_ALARM,"红外探测器"),
    /**
     * 资产定位标签
     */
    ASSET_LOCATION_TAG(5, SystemTypeEnum.ASSET_TRACKING, "资产定位标签");

    private Integer code;
    private SystemTypeEnum systemType;
    private String name;

    DeviceTypeEnum(int code, SystemTypeEnum systemType,String name) {
        this.code = code;
        this.systemType = systemType;
        this.name = name;
    }

    public static String getName(Integer code) {
        for (DeviceTypeEnum value : DeviceTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }
        return null;
    }

}

package com.rutong.medical.admin.service.alarm;

import com.rutong.medical.admin.dto.alarm.SoundLightAlarmDTO;
import com.rutong.medical.admin.vo.alarm.SoundLightAlarmVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-07-15
 * 声光报警设置服务接口
 */
public interface SoundLightAlarmService {

    /**
     * 批量更新报警配置（根据config_code和config_type）
     *
     * @param soundLightAlarmDTO 包含批量配置项的DTO
     * @return 更新结果
     */
    Boolean batchUpdateAlarmConfig(SoundLightAlarmDTO soundLightAlarmDTO);

    /**
     * 获取所有报警配置（带三级缓存）
     *
     * @return 所有报警配置列表
     */
    List<SoundLightAlarmVO> getAllAlarmConfigs();
}
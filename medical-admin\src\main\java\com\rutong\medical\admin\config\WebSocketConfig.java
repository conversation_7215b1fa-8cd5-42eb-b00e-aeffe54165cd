package com.rutong.medical.admin.config;

import com.alibaba.fastjson.JSON;
import com.rutong.medical.admin.constant.WebSocketTopicConstant;
import com.soft.common.core.object.TokenData;
import com.soft.common.core.util.JwtUtil;
import com.soft.common.core.util.RedisKeyUtil;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.simp.config.ChannelRegistration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptorAdapter;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

/**
 * @ClassName WebSocketConfig
 * @Description
 * <AUTHOR>
 * @Date 2025/7/7 19:25
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Slf4j
@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    @Autowired
    private ApplicationConfig applicationConfig;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private SimpMessagingTemplate simpMessagingTemplate;

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        registry.addEndpoint("/stomp")
                .setAllowedOrigins("*")

        // 开启sockJS协议头是http
//                .withSockJS()
        ;
    }

    @Override
    public void configureMessageBroker(MessageBrokerRegistry registry) {
        // 客户端接口地址前缀
        registry.setApplicationDestinationPrefixes("/web");
        // 启用用户目标支持
        registry.setUserDestinationPrefix("/user");
        // 服务端接口地址前缀
        registry.enableSimpleBroker("/topic/message", WebSocketTopicConstant.TOPIC_ALARM_SAFETY,WebSocketTopicConstant.TOPIC_ALARM_SAFETY,
                WebSocketTopicConstant.TOPIC_SECURITY, "/topic/location/single");
        // 集群需要配置用户广播和转发
//        registry.enableStompBrokerRelay("/topic/notice", "/topic/equipment")
//                .setRelayHost("*******")
//                .setRelayPort(61613)
//                .setClientLogin("admin")
//                .setClientPasscode("admin")
//                .setSystemLogin("admin")
//                .setSystemPasscode("admin")
//                .setVirtualHost("my-vhost")
//                .setUserDestinationBroadcast("/topic/unresolved-user")
//                .setUserRegistryBroadcast("/topic/user-registry");
    }

    @Override
    public void configureClientInboundChannel(ChannelRegistration registration) {
        registration.interceptors(new UserInterceptor());
    }

    public class UserInterceptor extends ChannelInterceptorAdapter {
        @Override
        public Message<?> preSend(Message<?> message, MessageChannel channel) {
            StompHeaderAccessor accessor =
                    MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
            if (StompCommand.CONNECT.equals(accessor.getCommand())) {
                String token = accessor.getFirstNativeHeader("Authorization");
                Claims c = JwtUtil.parseToken(token, applicationConfig.getTokenSigningKey());
                if(JwtUtil.isNullOrExpired(c)) {
                    return new GenericMessage<>(new Object());
                }
                String sessionIdKey = RedisKeyUtil.makeSessionIdKey((String) c.get("sessionId"));
                RBucket<String> sessionData = redissonClient.getBucket(sessionIdKey);
                TokenData tokenData = null;
                if (sessionData.isExists()) {
                    tokenData = JSON.parseObject(sessionData.get(), TokenData.class);
                }
                accessor.setUser(tokenData);
            }
            return message;
        }
    }
}

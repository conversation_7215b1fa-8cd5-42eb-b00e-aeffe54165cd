package com.rutong.medical.admin.service.location.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.rutong.medical.admin.constant.*;
import com.rutong.medical.admin.dto.station.BaseStationDataDTO;
import com.rutong.medical.admin.entity.alarm.AlarmDetail;
import com.rutong.medical.admin.entity.device.Device;
import com.rutong.medical.admin.entity.location.UserLocation;
import com.rutong.medical.admin.entity.station.DeviceBaseStation;
import com.rutong.medical.admin.entity.system.Space;
import com.rutong.medical.admin.mapper.alarm.AlarmDetailMapper;
import com.rutong.medical.admin.mapper.location.UserLocationTDMapper;
import com.rutong.medical.admin.service.device.DeviceService;
import com.rutong.medical.admin.service.location.UserLocationService;
import com.rutong.medical.admin.service.station.DeviceBaseStationService;
import com.rutong.medical.admin.service.system.SpaceService;
import com.rutong.medical.admin.vo.location.UserLocationVO;
import com.soft.admin.upms.enums.TagEnum;
import com.soft.admin.upms.model.SysDept;
import com.soft.admin.upms.model.SysTag;
import com.soft.admin.upms.model.SysUser;
import com.soft.admin.upms.service.SysDeptService;
import com.soft.admin.upms.service.SysUserService;
import com.soft.admin.upms.service.SysUserTagService;
import com.soft.common.core.constant.RedisKeyConstant;
import com.soft.common.core.object.TokenData;
import com.soft.common.core.util.MyModelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.codehaus.groovy.runtime.DefaultGroovyMethods.collect;

/**
 * @ClassName LocationServiceImpl
 * @Description 人员定位
 * <AUTHOR>
 * @Date 2025/7/15 10:28
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@AllArgsConstructor
@Service
@Slf4j
public class UserLocationServiceImpl implements UserLocationService {

    private UserLocationTDMapper userLocationMapper;
    private RedissonClient redissonClient;
    private AlarmDetailMapper alarmDetailMapper;
    private SysUserService sysUserService;
    private DeviceBaseStationService deviceBaseStationService;
    private SpaceService spaceService;
    private SysDeptService sysDeptService;
    private SimpMessagingTemplate simpMessagingTemplate;
    private SysUserTagService sysUserTagService;
    private DeviceService deviceService;

    private static final String LOCATION_TABLE_PREFIX = "alarm_location_detail_{deviceSn}_{deviceType}";
    /**
     * 缓存有效期
     */
    private static final Long EXPIRATION_TIME = 2L;


    @Override
    public Boolean cardSave(BaseStationDataDTO baseStationDataDTO) throws InvocationTargetException, IllegalAccessException {
        try {
            UserLocation userLocation = MyModelUtil.copyTo(baseStationDataDTO, UserLocation.class);
            DateTime reportDate = DateUtil.date(baseStationDataDTO.getTimestamp() * 1000);
            String currentDate = DateUtil.format(DateUtil.date(), "yyyyMMdd");
            String tableName = LOCATION_TABLE_PREFIX.replace("{deviceSn}", baseStationDataDTO.getDeviceSn())
                    .replace("{deviceType}", baseStationDataDTO.getDeviceType().toString());
            Long alarmDetailId = null;
            userLocation.setTableName(tableName);
            userLocation.setCreateTime(reportDate.toTimestamp());
            userLocation.setDeviceTypeCode(baseStationDataDTO.getDeviceType());
            userLocation.setBusinessCode(SystemTypeEnum.MEDICAL_SECURITY.getCode());

            SysUser sysUser = sysUserService.selectUserBydeviceSn(baseStationDataDTO.getDeviceSn());
            DeviceBaseStation baseStation = deviceBaseStationService.getDeviceBaseStation(baseStationDataDTO.getBaseStationSn());
            SysDept dept = null;
            SysTag sysTag = null;
            if (ObjectUtils.isEmpty(baseStation)) {
                return false;
            }
            if (ObjectUtils.isNotEmpty(sysUser)) {
                userLocation.setUserName(sysUser.getShowName());
                userLocation.setUserId(sysUser.getUserId());
                userLocation.setEmployeeNumber(sysUser.getEmployeeNumber());
                dept = sysDeptService.getCardDeptByUserId(sysUser.getUserId());
                sysTag = sysUserTagService.getTagByUserId(sysUser.getUserId());
            }
            if (ObjectUtils.isNotEmpty(dept)) {
                userLocation.setDeptName(dept.getDeptName());
                userLocation.setDeptCode(dept.getDeptCode());
            }
            Space space = spaceService.getSpaceCacheById(baseStation.getSpaceId());
            if (ObjectUtils.isNotEmpty(space)) {
                List<String> spaceIds = StrUtil.split(space.getPath(), '/');
                List<String> spaceNames = StrUtil.split(space.getFullName(), '/');
                userLocation.setBuildingId(Long.valueOf(spaceIds.get(1)));
                userLocation.setFloorId(Long.valueOf(spaceIds.get(2)));
                userLocation.setBuildingName(spaceNames.get(1));
                userLocation.setFloorName(spaceNames.get(2));
                userLocation.setPointName(space.getName());
                userLocation.setPointId(space.getId());
            }

            String redisKey = RedisKeyConstant.ALARM_DEVICE.replace("{currentDate}", currentDate);
            RMap<String, Long> alarmMap = redissonClient.getMap(redisKey);
            alarmMap.expire(EXPIRATION_TIME, TimeUnit.DAYS);
            if (alarmMap.containsKey(baseStationDataDTO.getDeviceSn())) {
                alarmDetailId = alarmMap.get(baseStationDataDTO.getDeviceSn());
                userLocation.setAlarmDetailId(alarmDetailId);
                userLocation.setIsAlarm(WhetherConstant._YES);
                userLocation.setAlarmType(AlarmTypeConstant.KEY_STATUS);
                simpMessagingTemplate.convertAndSend(WebSocketTopicConstant.TOPIC_ALARM_SAFETY, JSONObject.toJSONString(userLocation));
            } else {
                if (Objects.equals(baseStationDataDTO.getLowVoltageStatus(), WhetherConstant._YES) ||
                        Objects.equals(baseStationDataDTO.getKeyStatus(), WhetherConstant._YES)) {
                    userLocation.setAlarmType(AlarmTypeConstant.KEY_STATUS);
                    if (Objects.equals(baseStationDataDTO.getLowVoltageStatus(), WhetherConstant._YES)) {
                        userLocation.setAlarmType(AlarmTypeConstant.LOW_VLTAGE_STATUS);
                    }
                    userLocation.setIsKey(WhetherConstant._YES);
                    AlarmDetail alaramDetail = MyModelUtil.copyTo(userLocation, AlarmDetail.class);

                    alaramDetail.setDeviceBaseStationSn(userLocation.getBaseStationSn());
                    alaramDetail.setAlarmTime(userLocation.getCreateTime());
                    alaramDetail.setBusinessCode(SystemTypeEnum.MEDICAL_SECURITY.getCode());
                    alaramDetail.setDeviceTypeCode(userLocation.getDeviceTypeCode());
                    alaramDetail.setDisposeState(WhetherConstant._NO);
                    alaramDetail.setSpaceId(userLocation.getPointId());
                    if (ObjectUtils.isNotEmpty(sysUser)) {
                        alaramDetail.setUserId(sysUser.getUserId());
                        alaramDetail.setUserName(sysUser.getShowName());
                    }
                    alarmDetailMapper.insert(alaramDetail);
                    userLocation.setAlarmDetailId(alaramDetail.getId());

                    alarmMap.put(baseStationDataDTO.getDeviceSn(), alaramDetail.getId());
                    simpMessagingTemplate.convertAndSend(WebSocketTopicConstant.TOPIC_ALARM_SAFETY, JSONObject.toJSONString(userLocation));
                }
            }
            if (ObjectUtils.isNotEmpty(sysTag)) {
                userLocation.setTagCode(sysTag.getCode());
                if (Objects.equals(sysTag.getCode(), TagEnum.SECURITY)) {
                    simpMessagingTemplate.convertAndSend(WebSocketTopicConstant.TOPIC_SECURITY, JSONObject.toJSONString(userLocation));
                }
            }
            RMap<String, String> locationMap = redissonClient.getMap(RedisKeyConstant.LOCATION_CARD);
//            TokenData tokenData = TokenData.takeFromRequest();
//            if (ObjectUtils.isEmpty(tokenData)) {
//                String deviceSn = locationMap.get(TokenData.takeFromRequest().getToken());
//                if (StringUtils.isNotBlank(deviceSn) && Objects.equals(deviceSn, baseStationDataDTO.getDeviceSn())) {
//                    String topic = WebSocketTopicConstant.TOPIC_LOCATION_SINGLE.replace("{userId}", String.valueOf(sysUser.getUserId()));
//                    simpMessagingTemplate.convertAndSend(topic, JSONObject.toJSONString(userLocation));
//                }
//            }
            userLocationMapper.cardInsert(userLocation);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return true;
    }

    @Override
    public Boolean buttonSave(BaseStationDataDTO baseStationDataDTO) throws InvocationTargetException, IllegalAccessException {
        try {
            UserLocation userLocation = MyModelUtil.copyTo(baseStationDataDTO, UserLocation.class);
            DateTime reportDate = DateUtil.date(baseStationDataDTO.getTimestamp() * 1000);
            String currentDate = DateUtil.format(DateUtil.date(), "yyyyMMdd");
            String tableName = LOCATION_TABLE_PREFIX.replace("{deviceSn}", baseStationDataDTO.getDeviceSn())
                    .replace("{deviceType}", baseStationDataDTO.getDeviceType().toString());
            Long alarmDetailId = null;
            userLocation.setTableName(tableName);
            userLocation.setCreateTime(reportDate.toTimestamp());
            userLocation.setDeviceTypeCode(baseStationDataDTO.getDeviceType());
            userLocation.setTagCode(TagEnum.MEDICAL.name());

            Device device = deviceService.getDeviceBySn(baseStationDataDTO.getDeviceSn());
            if (ObjectUtils.isEmpty(device)) {
                return false;
            }
            if (ObjectUtils.isNotEmpty(device)) {
                List<String> spaceIds = StrUtil.split(device.getSpacePath(), '/');
                List<String> spaceNames = StrUtil.split(device.getSpaceFullName(), '/');

                if (spaceIds.size() >= 1 && spaceNames.size() >= 1) {
                    if (spaceIds.size() > 1) {
                        userLocation.setBuildingId(Long.valueOf(spaceIds.get(1)));
                        if (spaceNames.size() > 1) {
                            userLocation.setBuildingName(spaceNames.get(1));
                        }
                    }
                    if (spaceIds.size() > 2) {
                        userLocation.setFloorId(Long.valueOf(spaceIds.get(2)));
                        if (spaceNames.size() > 2) {
                            userLocation.setFloorName(spaceNames.get(2));
                        }
                    }
                    if (spaceIds.size() > 3) {
                        userLocation.setPointId(Long.valueOf(spaceIds.get(3)));
                        if (spaceNames.size() > 3) {
                            userLocation.setPointName(spaceNames.get(3));
                        }
                    }
                }

            }

            String redisKey = RedisKeyConstant.ALARM_DEVICE.replace("{currentDate}", currentDate);
            RMap<String, Long> alarmMap = redissonClient.getMap(redisKey);
            alarmMap.expire(EXPIRATION_TIME, TimeUnit.DAYS);
            if (alarmMap.containsKey(baseStationDataDTO.getDeviceSn())) {
                alarmDetailId = alarmMap.get(baseStationDataDTO.getDeviceSn());
                userLocation.setAlarmDetailId(alarmDetailId);
                userLocation.setIsAlarm(WhetherConstant._YES);
                userLocation.setAlarmType(AlarmTypeConstant.KEY_STATUS);
                if (Objects.equals(device.getBusinessCode(), SystemTypeEnum.MEDICAL_SECURITY.getCode())) {
                    userLocation.setBusinessCode(SystemTypeEnum.MEDICAL_SECURITY.getCode());
                    simpMessagingTemplate.convertAndSend(WebSocketTopicConstant.TOPIC_ALARM_SAFETY, JSONObject.toJSONString(userLocation));
                } else if (Objects.equals(device.getBusinessCode(), SystemTypeEnum.WIRELESS_ALARM.getCode())) {
                    userLocation.setBusinessCode(SystemTypeEnum.WIRELESS_ALARM.getCode());
                    simpMessagingTemplate.convertAndSend(WebSocketTopicConstant.TOPIC_ALARM_INVADE, JSONObject.toJSONString(userLocation));
                }
            } else {
                if (Objects.equals(baseStationDataDTO.getLowVoltageStatus(), WhetherConstant._YES) ||
                        Objects.equals(baseStationDataDTO.getKeyStatus(), WhetherConstant._YES)) {
                    userLocation.setAlarmType(AlarmTypeConstant.KEY_STATUS);
                    if (Objects.equals(baseStationDataDTO.getLowVoltageStatus(), WhetherConstant._YES)) {
                        userLocation.setAlarmType(AlarmTypeConstant.LOW_VLTAGE_STATUS);
                    }
                    userLocation.setIsKey(WhetherConstant._YES);
                    AlarmDetail alaramDetail = MyModelUtil.copyTo(userLocation, AlarmDetail.class);
                    alaramDetail.setDeviceBaseStationSn(userLocation.getBaseStationSn());
                    alaramDetail.setAlarmTime(userLocation.getCreateTime());
                    alaramDetail.setDeviceTypeCode(userLocation.getDeviceTypeCode());
                    alaramDetail.setDisposeState(WhetherConstant._NO);
                    alaramDetail.setSpaceId(userLocation.getPointId());
                    userLocation.setAlarmDetailId(alaramDetail.getId());
                    if (Objects.equals(device.getBusinessCode(), SystemTypeEnum.MEDICAL_SECURITY.getCode())) {
                        userLocation.setBusinessCode(SystemTypeEnum.MEDICAL_SECURITY.getCode());
                        simpMessagingTemplate.convertAndSend(WebSocketTopicConstant.TOPIC_ALARM_SAFETY, JSONObject.toJSONString(userLocation));
                    } else if (Objects.equals(device.getBusinessCode(), SystemTypeEnum.WIRELESS_ALARM.getCode())) {
                        userLocation.setBusinessCode(SystemTypeEnum.WIRELESS_ALARM.getCode());
                        simpMessagingTemplate.convertAndSend(WebSocketTopicConstant.TOPIC_ALARM_INVADE, JSONObject.toJSONString(userLocation));
                    }
                    alarmDetailMapper.insert(alaramDetail);
                    alarmMap.put(baseStationDataDTO.getDeviceSn(), alaramDetail.getId());
                }
            }

            userLocationMapper.buttonInsert(userLocation);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return true;
    }

    @Override
    public Boolean infraredMonitorSave(BaseStationDataDTO baseStationDataDTO) throws InvocationTargetException, IllegalAccessException {
        try {
            UserLocation userLocation = MyModelUtil.copyTo(baseStationDataDTO, UserLocation.class);
            DateTime reportDate = DateUtil.date(baseStationDataDTO.getTimestamp() * 1000);
            String currentDate = DateUtil.format(DateUtil.date(), "yyyyMMdd");
            String tableName = LOCATION_TABLE_PREFIX.replace("{deviceSn}", baseStationDataDTO.getDeviceSn())
                    .replace("{deviceType}", baseStationDataDTO.getDeviceType().toString());
            Long alarmDetailId = null;
            userLocation.setTableName(tableName);
            userLocation.setCreateTime(reportDate.toTimestamp());
            userLocation.setDeviceTypeCode(baseStationDataDTO.getDeviceType());
            userLocation.setTagCode(TagEnum.MEDICAL.name());

            Device device = deviceService.getDeviceBySn(baseStationDataDTO.getDeviceSn());
            if (ObjectUtils.isEmpty(device)) {
                return false;
            }
            if (ObjectUtils.isNotEmpty(device)) {
                List<String> spaceIds = StrUtil.split(device.getSpacePath(), '/');
                List<String> spaceNames = StrUtil.split(device.getSpaceFullName(), '/');
                userLocation.setSpaceId(device.getSpaceId());
                if (spaceIds.size() >= 1 && spaceNames.size() >= 1) {
                    if (spaceIds.size() > 1) {
                        userLocation.setBuildingId(Long.valueOf(spaceIds.get(1)));
                        if (spaceNames.size() > 1) {
                            userLocation.setBuildingName(spaceNames.get(1));
                        }
                    }
                    if (spaceIds.size() > 2) {
                        userLocation.setFloorId(Long.valueOf(spaceIds.get(2)));
                        if (spaceNames.size() > 2) {
                            userLocation.setFloorName(spaceNames.get(2));
                        }
                    }
                    if (spaceIds.size() > 3) {
                        userLocation.setPointId(Long.valueOf(spaceIds.get(3)));
                        if (spaceNames.size() > 3) {
                            userLocation.setPointName(spaceNames.get(3));
                        }
                    }
                }

            }

            String redisKey = RedisKeyConstant.ALARM_DEVICE.replace("{currentDate}", currentDate);
            RMap<String, Long> alarmMap = redissonClient.getMap(redisKey);
            alarmMap.expire(EXPIRATION_TIME, TimeUnit.DAYS);
            userLocation.setIsKey(WhetherConstant._YES);
            userLocation.setBusinessCode(SystemTypeEnum.WIRELESS_ALARM.getCode());
            if (alarmMap.containsKey(baseStationDataDTO.getDeviceSn())) {
                alarmDetailId = alarmMap.get(baseStationDataDTO.getDeviceSn());
                userLocation.setAlarmDetailId(alarmDetailId);
                userLocation.setIsAlarm(WhetherConstant._YES);
                userLocation.setAlarmType(AlarmTypeEnum.INFRARED_INTRUSION.getCode().byteValue());
                simpMessagingTemplate.convertAndSend(WebSocketTopicConstant.TOPIC_ALARM_INVADE, JSONObject.toJSONString(userLocation));
            } else {
                if (Objects.equals(baseStationDataDTO.getLowVoltageStatus(), WhetherConstant._YES) ||
                        Objects.equals(baseStationDataDTO.getInfraredStatus(), WhetherConstant._YES)) {
                    userLocation.setAlarmType(AlarmTypeEnum.INFRARED_INTRUSION.getCode().byteValue());
                    if (Objects.equals(baseStationDataDTO.getLowVoltageStatus(), WhetherConstant._YES)) {
                        userLocation.setAlarmType(AlarmTypeConstant.LOW_VLTAGE_STATUS);
                    }
                    AlarmDetail alaramDetail = MyModelUtil.copyTo(userLocation, AlarmDetail.class);
                    alaramDetail.setDeviceBaseStationSn(userLocation.getBaseStationSn());
                    alaramDetail.setAlarmTime(userLocation.getCreateTime());
                    alaramDetail.setDeviceTypeCode(userLocation.getDeviceTypeCode());
                    alaramDetail.setDisposeState(WhetherConstant._NO);
                    alaramDetail.setSpaceId(device.getSpaceId());
                    userLocation.setAlarmDetailId(alaramDetail.getId());
                    simpMessagingTemplate.convertAndSend(WebSocketTopicConstant.TOPIC_ALARM_INVADE, JSONObject.toJSONString(userLocation));
                    alarmDetailMapper.insert(alaramDetail);
                    alarmMap.put(baseStationDataDTO.getDeviceSn(), alaramDetail.getId());
                }
            }

            userLocationMapper.buttonInsert(userLocation);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return true;
    }

    @Override
    public Boolean subscriptionSingle(Long userId) {
        try {
            TokenData tokenData = TokenData.takeFromRequest();
            String token = tokenData.getToken();
            RMap<String, Long> locationMap = redissonClient.getMap(RedisKeyConstant.LOCATION_CARD);
            locationMap.put(token, userId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
        return true;
    }

    @Override
    public List<UserLocationVO> getSecurityAll() {
        return sysUserService.getUserByTagCode(TagEnum.SECURITY.name()).stream().map(sysUser -> {
            UserLocationVO userLocationVO = userLocationMapper.selectUserLocationByDeviceSn(sysUser.getDeviceSn());
            if (ObjectUtils.isNotEmpty(userLocationVO)) {
                if (Objects.equals(userLocationVO.getNewLocatorSn(), "0")) {
                    userLocationVO.setNewLocatorSn(userLocationVO.getOldLocatorSn());
                }
            } else {
                userLocationVO = new UserLocationVO();
            }
            userLocationVO.setUserId(sysUser.getUserId());
            userLocationVO.setUserName(sysUser.getShowName());
            userLocationVO.setDeviceSn(sysUser.getDeviceSn());
            return userLocationVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<UserLocationVO> getUserLocationByFloor(Long floorId, String tagCode) {
        return userLocationMapper.selectUserLocationByFloor(floorId, tagCode);
    }

}

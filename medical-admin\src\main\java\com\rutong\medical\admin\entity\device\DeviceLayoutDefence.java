package com.rutong.medical.admin.entity.device;

import com.baomidou.mybatisplus.annotation.TableName;
import com.rutong.medical.admin.vo.device.DeviceVO;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;
import lombok.Data;
import org.mapstruct.Mapper;

import java.time.LocalTime;

/**
 * 设备布防表
 *
 * <AUTHOR>
 * @Date 2025-07-31
 */
@Data
@TableName(value = "sm_device_layout_defence")
public class DeviceLayoutDefence extends BaseModel {

    /**
     * 设备布防表ID
     */
    private Long id;
    /**
     * 设备表ID
     */
    private Long deviceId;
    /**
     * 防区表ID
     */
    private Long invadeDefenceId;
    /**
     * 布防状态
     */
    private Integer defenceState;
    /**
     * 布防开启时间
     */
    private LocalTime startTime;
    /**
     * 布防结束时间
     */
    private LocalTime endTime;

    @Mapper
    public interface SmDeviceModelMapper extends BaseModelMapper<DeviceVO, Device> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        Device toModel(DeviceVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        DeviceVO fromModel(Device entity);
    }

}

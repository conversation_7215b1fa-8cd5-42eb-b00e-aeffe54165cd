<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.device.DeviceMapper">
    <resultMap type="com.rutong.medical.admin.entity.device.Device" id="SmDeviceResult">
        <result property="id" column="id"/>
        <result property="deviceTerminalTypeId" column="device_terminal_type_id"/>
        <result property="deviceCode" column="device_code"/>
        <result property="deviceName" column="device_name"/>
        <result property="businessCode" column="business_code"/>
        <result property="spaceId" column="space_id"/>
        <result property="spacePath" column="space_path"/>
        <result property="spaceFullName" column="space_full_name"/>
        <result property="x" column="x"/>
        <result property="y" column="y"/>
        <result property="z" column="z"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="isOnline" column="is_online"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUserId" column="update_user_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <sql id="selectSmDeviceVo">
        select id,
               device_terminal_type_id,
               device_code,
               device_name,
               business_code,
               space_id,
               space_path,
               space_full_name,
               x,
               y,
               z,
               longitude,
               latitude,
               is_online,
               create_user_id,
               create_time,
               update_user_id,
               update_time,
               is_delete
        from sm_device
    </sql>

    <select id="list" resultType="com.rutong.medical.admin.entity.device.Device"
            parameterType="com.rutong.medical.admin.dto.station.DevicePageQueryDTO">
        select * from sm_device
        <where>
            <!-- 查询未删除的数据 -->
            and is_delete = 1

            <!-- 关键词搜索：基站名称或编号 -->
            <if test="keyWord != null and keyWord != ''">
                and (device_code like concat('%', #{keyWord}, '%')
                or device_name like concat('%', #{keyWord}, '%'))
            </if>

            <!-- 匹配所属楼层路径 -->
            <if test="spacePath != null and spacePath != ''">
                and space_path like concat('%', #{spacePath}, '%')
            </if>

            <if test="businessCode != null and businessCode != ''">
                and business_code like concat('%', #{businessCode}, '%')
            </if>

            <if test="deviceTerminalTypeIdList != null">
                and device_terminal_type_id in
                <foreach item="item" index="index" collection="deviceTerminalTypeIdList" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 在线状态 -->
            <if test="isOnline != null">
                and is_online = #{isOnline}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="existsByDeviceNumberOrName" resultType="int">
        SELECT COUNT(*)
        FROM sm_device
        WHERE (device_code = #{param1} OR device_name = #{param2})
          and is_delete = 1
    </select>


    <select id="existsByDeviceNumberOrNameExcludingId" resultType="int">
        SELECT COUNT(*)
        FROM sm_device
        WHERE (device_code = #{param1}OR device_name = #{param2})
          AND id != #{param3}
          and is_delete = 1
    </select>
    <select id="selectDeviceBySn" resultMap="SmDeviceResult">
        select t.device_code, t.space_path, t.space_full_name, t.business_code, t.space_id
        from sm_device t
        where device_sn = #{deviceSn}
    </select>

    <select id="selectDevicePage" resultType="com.rutong.medical.admin.vo.device.DeviceBusinessVO"
            parameterType="com.rutong.medical.admin.dto.station.DevicePageQueryDTO">
        select t.device_id,t.device_sn,t.device_name,t.show_name,t.dept_name,t.low_battery_state,
        t.call_state,t.split_state,t.infrared_state,t.defence_state,t.space_full_name
        from (select a.id as device_id,
        a.device_sn,
        a.device_name,
        b.show_name,
        (select GROUP_CONCAT(y.dept_name)
        from common_sys_dept_user x,
        common_sys_dept y
        where x.dept_id = y.dept_id
        and x.user_id = b.user_id) as dept_name,
        COALESCE((select t.dispose_state
        from sm_alarm_detail t
        where t.device_sn = a.device_sn
        and t.alarm_type = 1
        order by alarm_time desc LIMIT 1),1) as low_battery_state,
        COALESCE((select t.dispose_state from sm_alarm_detail t where t.device_sn=a.device_sn and t.alarm_type=2 order
        by alarm_time desc LIMIT 1),1) as call_state,
        COALESCE((select t.dispose_state from sm_alarm_detail t where t.device_sn=a.device_sn and t.alarm_type=3 order
        by alarm_time desc LIMIT 1),1) as split_state,
        COALESCE((select t.dispose_state from sm_alarm_detail t where t.device_sn=a.device_sn and t.alarm_type=4 order
        by alarm_time desc LIMIT 1),1) as infrared_state,
        (select t.defence_state from sm_device_layout_defence t where t.device_id=a.id) as defence_state,
        (select t.full_name from sp_space t where t.id=a.space_id) as space_full_name
        from sm_device a left join common_sys_user b on a.device_sn=b.device_sn ) t
        <where>
            <if test="deviceCondition != null and deviceCondition != ''">
                and (t.device_name like concat('%', #{deviceCondition}, '%')
                or t.device_sn like concat('%', #{deviceCondition}, '%')
                or t.show_name like concat('%', #{deviceCondition}, '%'))
            </if>
            <if test="deviceState != null and deviceState==0 ">
                and (low_battery_state=#{deviceState} or call_state=#{deviceState} or split_state=#{deviceState} or
                infrared_state=#{deviceState})
            </if>
            <if test="deviceState != null and deviceState==1 ">
                and (low_battery_state=#{deviceState} and call_state=#{deviceState} and split_state=#{deviceState} and
                infrared_state=#{deviceState})
            </if>
        </where>
    </select>


    <!-- 更新设备信息 -->
    <update id="updateDeviceById">
        UPDATE sm_device
        <set>
            <!-- 设备分类表ID -->
            <if test="deviceTerminalTypeId != null">device_terminal_type_id = #{deviceTerminalTypeId},</if>

            <!-- 设备编号 -->
            <if test="deviceCode != null">device_code = #{deviceCode},</if>

            <!-- 设备名称 -->
            <if test="deviceName != null">device_name = #{deviceName},</if>

            <!-- 业务系统编号 -->
            <if test="businessCode != null">business_code = #{businessCode},</if>

            <!-- 安装位置 -->
            <if test="spaceId != null">space_id = #{spaceId},</if>

            <!-- 所属楼层路径 -->
            <if test="spacePath != null">space_path = #{spacePath},</if>

            <!-- 所属楼层全名称 -->
            <if test="spaceFullName != null">space_full_name = #{spaceFullName},</if>

            <!-- x -->
            <if test="x != ''">x = #{x},</if>

            <!-- y -->
            <if test="y != ''">y = #{y},</if>

            <!-- z -->
            <if test="z != ''">z = #{z},</if>

            <!-- 经度（强制更新，即使为 null） -->
            <if test="longitude != ''">longitude = #{longitude},</if>

            <!-- 纬度（强制更新，即使为 null） -->
            <if test="latitude != ''">latitude = #{latitude},</if>

            <!-- 在线状态 -->
            <if test="isOnline != null">is_online = #{isOnline},</if>

            <!-- 终端sn -->
            <if test="deviceSn != null">device_sn = #{deviceSn},</if>

            <!-- 更新时间 -->
            <if test="updateTime != null">update_time = #{updateTime},</if>

            <!-- 更新人ID -->
            <if test="updateUserId != null">update_user_id = #{updateUserId},</if>
        </set>
        WHERE id = #{id}
    </update>

</mapper>
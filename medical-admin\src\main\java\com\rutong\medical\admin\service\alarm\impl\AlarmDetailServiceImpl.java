package com.rutong.medical.admin.service.alarm.impl;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.rutong.medical.admin.constant.AlarmTypeEnum;
import com.rutong.medical.admin.constant.WhetherConstant;
import com.rutong.medical.admin.dto.alarm.AlarmDetailPageQueryDTO;
import com.rutong.medical.admin.dto.alarm.AlarmDisposeDTO;
import com.rutong.medical.admin.entity.alarm.AlarmDetailMonitorImg;
import com.rutong.medical.admin.entity.system.Space;
import com.rutong.medical.admin.mapper.alarm.AlarmDetailMonitorImgMapper;
import com.rutong.medical.admin.mapper.alarm.AlarmDetailTDMapper;
import com.rutong.medical.admin.mapper.system.SpaceMapper;
import com.rutong.medical.admin.vo.alarm.AlarmDetailMonitorImgVO;
import com.rutong.medical.admin.vo.alarm.AlarmDetailTDVO;
import com.rutong.medical.admin.vo.alarm.AlarmDetailVO;
import com.soft.admin.upms.dao.SysDeptMapper;
import com.soft.admin.upms.dao.SysUserDeptMapper;
import com.soft.admin.upms.dao.SysUserMapper;
import com.soft.admin.upms.model.SysDept;
import com.soft.admin.upms.model.SysUser;
import com.soft.admin.upms.model.SysUserDept;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.util.MyModelUtil;
import com.soft.common.core.util.MyPageUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rutong.medical.admin.entity.alarm.AlarmDetail;
import com.rutong.medical.admin.mapper.alarm.AlarmDetailMapper;
import com.rutong.medical.admin.service.alarm.AlarmDetailService;

/**
 * @ClassName AlarmDetailServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/7/15 10:24
 * @Version 1.0
 * @Copyright © 2025 All Rights Reserved
 */
@Service
@AllArgsConstructor
public class AlarmDetailServiceImpl extends ServiceImpl<AlarmDetailMapper, AlarmDetail> implements AlarmDetailService {

    private AlarmDetailMapper alarmDetailMapper;
    private AlarmDetailTDMapper alarmDetailTDMapper;
    private SysUserMapper sysUserMapper;
    private SysUserDeptMapper sysUserDeptMapper;
    private SysDeptMapper sysDeptMapper;
    private SpaceMapper spaceMapper;
    private AlarmDetailMonitorImgMapper alarmDetailMonitorImgMapper;

    @Override
    public Map<String, String> getDeviceStatus(List<String> deviceSns) {
        // 查询所有未处理的告警信息
        List<AlarmDetail> alarmDetails = alarmDetailMapper.selectList(Wrappers.lambdaQuery(AlarmDetail.class)
                .in(AlarmDetail::getDeviceSn, deviceSns).eq(AlarmDetail::getDisposeState, 0));

        // 将 alarmDetails 转为 Map<String, List<AlarmDetail>>，key 是 deviceSn
        Map<String, List<AlarmDetail>> alarmMap =
                alarmDetails.stream().collect(Collectors.groupingBy(AlarmDetail::getDeviceSn));

        // 构建结果Map
        Map<String, String> result = new HashMap<>();
        for (String deviceSn : deviceSns) {
            List<AlarmDetail> alarmDetailList = alarmMap.get(deviceSn);
            if (CollectionUtils.isNotEmpty(alarmDetailList)) {
                result.put(deviceSn, deviceSn);
            }

        }
        return result;
    }

    @Override
    public List<AlarmDetailTDVO> getAlarmDetailList(String businessCode) {
        Date beginOfDay = DateUtil.beginOfDay(new Date());
        Date endOfDay = DateUtil.endOfDay(new Date());
        return alarmDetailTDMapper.selectAlarmDetailList(businessCode, beginOfDay, endOfDay);
    }

    @Override
    public MyPageData<AlarmDetailVO> page(AlarmDetailPageQueryDTO alarmDetailPageQueryDTO) {
        Integer pageNum = alarmDetailPageQueryDTO.getPageNum();
        Integer pageSize = alarmDetailPageQueryDTO.getPageSize();
        if (ObjectUtils.isNotEmpty(pageNum) && ObjectUtils.isNotEmpty(pageSize)) {
            PageHelper.startPage(pageNum, pageSize).toPageInfo();
        }
        List<AlarmDetailVO> list = alarmDetailMapper.pageList(alarmDetailPageQueryDTO);

        return MyPageUtil.makeResponseData(list);
    }

    @Override
    public boolean dispose(AlarmDisposeDTO alarmDisposeDTO) {
        AlarmDetail alarmDetail = MyModelUtil.copyTo(alarmDisposeDTO, AlarmDetail.class);
        alarmDetail.setDisposeState(WhetherConstant._YES);
        alarmDetail.setDisposeTime(DateUtil.date());
        return alarmDetailMapper.updateById(alarmDetail) > 0;
    }

    @Override
    public AlarmDetailVO detail(Long id) {
        AlarmDetail alarmDetail = alarmDetailMapper.selectById(id);
        if (ObjectUtils.isEmpty(alarmDetail)) {
            return null;
        }
        AlarmDetailVO alarmDetailVO = MyModelUtil.copyTo(alarmDetail, AlarmDetailVO.class);
        SysUserDept sysUserDept = sysUserDeptMapper.selectOne(SysUserDept::getUserId, alarmDetail.getUserId());
        SysDept sysDept = null;
        if (ObjectUtils.isNotEmpty(sysUserDept)) {
            sysDept = sysDeptMapper.selectById(sysUserDept.getDeptId());
        }
        if (ObjectUtils.isNotEmpty(sysDept)) {
            alarmDetailVO.setDeptName(sysDept.getDeptName());
        }
        Space space = spaceMapper.selectById(alarmDetail.getSpaceId());
        if (ObjectUtils.isNotEmpty(space)) {
            alarmDetailVO.setSpaceFullName(space.getFullName());
        }
        List<AlarmDetailMonitorImg> alarmDetailMonitorImgList = alarmDetailMonitorImgMapper.selectList(
                Wrappers.lambdaQuery(AlarmDetailMonitorImg.class)
                        .eq(AlarmDetailMonitorImg::getAlarmDetailId, alarmDetail.getId()));
        if (CollectionUtil.isNotEmpty(alarmDetailMonitorImgList)) {
            alarmDetailVO.setAlarmDetailMonitorImgList(AlarmDetailMonitorImg.INSTANCE.fromModelList(alarmDetailMonitorImgList));
        }
        return alarmDetailVO;
    }

}

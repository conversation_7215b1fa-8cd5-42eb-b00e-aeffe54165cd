package com.rutong.medical.admin.mapper.device;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rutong.medical.admin.dto.station.DevicePageQueryDTO;
import com.rutong.medical.admin.entity.device.Device;

import com.rutong.medical.admin.vo.device.DeviceBusinessVO;
import com.rutong.medical.admin.vo.device.DeviceVO;
import com.soft.common.core.object.MyPageData;
import feign.Param;

/**
 * 设备Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-12
 */
public interface DeviceMapper extends BaseMapper<Device> {

    /**
     * 
     * @param devicePageQueryDTO
     * @return
     */
    List<Device> list(DevicePageQueryDTO devicePageQueryDTO);

    /**
     * 设备编号或设备名称是否存在
     *
     * @param params
     * @return
     */
    int existsByDeviceNumberOrName(@Param("deviceCode") String deviceCode, @Param("deviceName") String deviceName);

    /**
     * 设备编号或设备名称是否存在 不包括自身
     * 
     * @param params
     * @return
     */
    int existsByDeviceNumberOrNameExcludingId(@Param("deviceCode") String deviceCode,
        @Param("deviceName") String deviceName, @Param("id") Long id);

    /**
     * 根据更新设备信息
     * 
     * @param id
     */
    void updateDeviceById(Device device);

    /**
     * 根据设备sn查询设备信息
     * @param deviceSn
     * @return
     */
    Device selectDeviceBySn(String deviceSn);

    /**
     * 终端查询列表
     * @param devicePageQueryDTO
     * @return
     */
    List<DeviceBusinessVO> selectDevicePage(DevicePageQueryDTO devicePageQueryDTO);
}

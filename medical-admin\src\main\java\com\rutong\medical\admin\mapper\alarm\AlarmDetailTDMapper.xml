<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.alarm.AlarmDetailTDMapper">


    <select id="selectAlarmDetailList" resultType="com.rutong.medical.admin.vo.alarm.AlarmDetailTDVO">
        SELECT device_type_code as deviceTypeCode,device_sn as deviceSn,dept_name as deptName,alarm_type as alarmType,
        FIRST (new_locator_sn) AS newLocatorSn, FIRST (floor_id) AS floorId, FIRST (point_id) AS pointId,
        FIRST (create_time) AS createTime, sum (is_key) AS isKeyCount
        FROM
        alarm_location_detail
        WHERE
        is_alarm = 1
        AND create_time >= #{startTime}
        AND create_time &lt;= #{endTime}
        <if test="businessCode != null and businessCode != ''">
            AND business_code = #{businessCode}
        </if>
        GROUP BY
        device_type_code,
        device_sn,
        alarm_type,
        dept_name

    </select>

</mapper>

package com.rutong.medical.admin.controller.device;

import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2025-07-31
 */
@RestController
@RequestMapping("/deviceArm")
public class DeviceArmController {

//    @Autowired
//    private DeviceArmService deviceArmService;
//
//    @ApiOperation(value="布防撤防")
//    @PostMapping("/arm")
//    public


}
